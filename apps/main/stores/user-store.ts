import { create } from 'zustand';
import { removeAccessToken, setAccessToken } from '@/queries/authentication/access-token';
import Cookies from 'js-cookie';
import { ACCOUNT_KEY, TOKEN_KEY, USER_KEY } from '@/constants';
import { User, UserAccount } from '@/types/authentication';

interface UserState {
  userToken: string | null;
  profilePicture: string | undefined;
  userData: User | null;
  userAccount: UserAccount | null;
  setProfilePicture: (url: string | undefined) => void;
  setUserData: (data: User) => void;
  setUserAccount: (data: UserAccount) => void;
  handleLogout: () => void;
  isAuthModalOpen: boolean;
  setAuthModalOpen: (isOpen: boolean) => void;
  isGetProfilePending: boolean;
  setIsGetProfilePending: (isLoading: boolean) => void;
  updateUserCookies: (user: User, account: UserAccount) => void;
  setUserToken: (value: string | null) => void;
  removeAllUserData: () => void;
}

export const useUserStore = create<UserState>((set, get) => ({
  profilePicture: undefined,
  isGetProfilePending: false,
  userData: Cookies.get(USER_KEY) ? JSON.parse(Cookies.get(USER_KEY) as string) : null,
  userAccount: Cookies.get(ACCOUNT_KEY) ? JSON.parse(Cookies.get(ACCOUNT_KEY) as string) : null,
  isAuthModalOpen: false,
  userToken: Cookies.get(TOKEN_KEY) ?? null,

  setUserToken: (value) => {
    if (value) {
      Cookies.set(TOKEN_KEY, value, { expires: 30, path: '/' });
    } else {
      Cookies.remove(TOKEN_KEY);
    }

    set({ userToken: value });
  },

  setProfilePicture: (url) => {
    set({ profilePicture: url });
  },

  setAuthModalOpen: (isOpen) => {
    set({ isAuthModalOpen: isOpen });
  },

  setIsGetProfilePending: (isLoading) => {
    set({ isGetProfilePending: isLoading });
  },

  setUserData: (data) => {
    Cookies.set(USER_KEY, JSON.stringify(data), { expires: 30, path: '/' });
    set({ userData: data });
  },

  setUserAccount: (data) => {
    Cookies.set(ACCOUNT_KEY, JSON.stringify({ ...data, guest_commercial_registration: null }), {
      expires: 30,
      path: '/',
    });
    set({ userAccount: data });
  },

  removeAllUserData: () => {
    set({ profilePicture: undefined, userData: null, userToken: null });
    Cookies.remove(TOKEN_KEY);
    Cookies.remove(USER_KEY);
    Cookies.remove(ACCOUNT_KEY);
  },

  handleLogout: (onSuccess?: () => void) => {
    get().removeAllUserData();
    onSuccess?.();
  },

  updateUserCookies: (user, account) => {
    Cookies.set(USER_KEY, JSON.stringify(user));
    Cookies.set(ACCOUNT_KEY, JSON.stringify(account));
    set({ userData: user, userAccount: account });
  },
}));

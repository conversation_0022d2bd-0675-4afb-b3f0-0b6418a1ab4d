import { CSSProperties, type FC, type ReactElement, useEffect } from 'react';
import LightGallery from 'lightgallery/react';
import 'lightgallery/css/lightgallery.css';
import 'lightgallery/css/lg-thumbnail.css';
import 'lightgallery/css/lg-zoom.css';
import lgThumbnail from 'lightgallery/plugins/thumbnail';
import lgZoom from 'lightgallery/plugins/zoom';
// import ZoomImage from '../ui/zoom-image';
import { useGetImageDimensions } from '@/queries/property-details';
import { Photos } from '@/types/homepage';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import styles from './styles.module.css';
import ZoomImage from '@/components/ui/zoom-image';

type PropertyDetailsPicturesTabProps = {
  photos: Photos[];
};

const galleryItemClasses = [
  'col-start-2 col-end-3 row-start-1 row-end-2',
  'col-start-1 col-end-2 row-start-1 row-end-3',
  'col-start-2 col-end-3 row-start-2 row-end-4',
  'col-start-1 col-end-2 row-start-3 row-end-5',
  'col-start-2 col-end-3 row-start-4 row-end-6',
  'col-start-1 col-end-2 row-start-5 row-end-7',
  'col-start-2 col-end-3 row-start-6 row-end-8',
];
const gridRowsMobileSizes = ['152px', '55px', '97px', '15px', '64px', '121px', '72px'];
const gridRowsDesktopSizes = ['355px', '130px', '225px', '36px', '148px', '283px', '166px'];
interface GalleryImageItemProps {
  src: string;
  alt: string;
  index: number;
  className: string;
}
const GalleryImageItem: FC<GalleryImageItemProps> = ({ src, alt, index, className = '' }) => {
  return (
    <a
      title='View Image'
      key={src}
      href={src}
      className={`${className}  gallery-item mobile:rounded-[24px]  block flex-none overflow-hidden rounded-[16px]`}>
      <ZoomImage alt={alt} image={src} withZoom={false} />
    </a>
  );
};

const PropertyDetailsPicturesTab: FC<PropertyDetailsPicturesTabProps> = ({ photos }): ReactElement => {
  const imageDms = useGetImageDimensions();
  const t = useScopedI18n('property_details');

  useEffect(() => {
    imageDms.mutate({ images: photos });
  }, [photos]);

  return (
    <>
      <div>
        <div
          className={styles.wrapper}
          style={
            {
              '--grid-rows-mobile': `${gridRowsMobileSizes.slice(0, (imageDms?.data ?? [])?.length - 1).join(' ')}`,
              '--grid-rows-desktop': `${gridRowsDesktopSizes.slice(0, (imageDms?.data ?? [])?.length - 1).join(' ')}`,
            } as CSSProperties
          }>
          <LightGallery elementClassNames="gallery-item" plugins={[lgZoom, lgThumbnail]} speed={500}>
            {imageDms?.data?.map((photo, index) => (
              <GalleryImageItem
                className={galleryItemClasses[index]}
                src={photo?.src}
                alt={`${t('property_image')} ${index + 1}`}
                index={index}
              />
            ))}
          </LightGallery>
        </div>
      </div>
    </>
  );
};
export default PropertyDetailsPicturesTab;

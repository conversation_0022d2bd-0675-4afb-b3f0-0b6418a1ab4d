import { useScopedI18n } from '@/lib/i18n/client-translator';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Input, Label as InputLabel } from '@/components/hostPages/common';
import IconWrapper from '@/components/icons';
import { format } from 'date-fns';

// Helper function to check if a value is a valid date
const isValidDate = (date: any): date is Date => {
  return date instanceof Date && !isNaN(date.getTime());
};

interface DatePickerProps {
  value: Date | string | null | undefined;
  name: string;
  errorMsg?: boolean;
  placeholder: string;
  disabled?: (date: Date) => boolean;
  triggerDisabled?: boolean;
}

export const DatePicker = ({
  value,
  name,
  errorMsg,
  placeholder,
  disabled,
  triggerDisabled,
}: DatePickerProps) => {
  const t = useScopedI18n('authentication');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<Date | null>(null); // Default to current date if no value

  const { register, setValue } = useFormContext();

  const onSelectDate = (date: Date) => {
    if (isValidDate(date)) {
      setIsOpen(false);
      setValue(name, date, { shouldDirty: true });
    } else {
      console.error('Invalid date selected');
    }
  };

  const onMonthChange = (month: Date) => {
    if (isValidDate(month)) {
      setSelectedMonth(month);
    } else {
      console.error('Invalid month selected');
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <div className="flex flex-col">
        <InputLabel label={t(placeholder as any)} />

        <PopoverTrigger className="text-start" disabled={triggerDisabled}>
          <Input
            {...register(name)}
            placeholder={t(placeholder as keyof typeof t)}
            prefix={<IconWrapper name="Calendar" size={16} className="text-secondary-300" />}
            inputProps={{ readOnly: true }}
            value={typeof value === 'string' ? value : (isValidDate(value) ? format(value, 'yyyy-MM-dd') : '')}
            errMsg={errorMsg}
          />
        </PopoverTrigger>

        <PopoverContent>
          <Calendar
            captionLayout="dropdown"
            // fromYear={fromYear}
            // toYear={toYear}
            mode="single"
            onSelect={onSelectDate}
            selected={isValidDate(value) ? value : undefined}
            month={selectedMonth || (isValidDate(value) ? value : undefined)}
            onMonthChange={onMonthChange} // Attach the onMonthChange function here
            classNames={{
              month_caption: 'h-[65px] px-10 ',
              nav: 'flex items-start z-10 absolute w-full justify-between items-center top-0 z-0 translate-y-[10px]',
            }}
            disabled={disabled}
          />
        </PopoverContent>
      </div>
    </Popover>
  );
};

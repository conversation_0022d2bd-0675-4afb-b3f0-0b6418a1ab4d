import React, { useEffect } from 'react';
import { useGetVerificationCountries } from '@/queries/host-pages/my-account';
import CusSelect from '../Select';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { arrToHashTable } from '@/components/hostPages/utils';
import { StepsBtn } from './steps-btn';
import useVerifyState from '../hooks/useVerifyState';

const ChooseCountries = () => {
  const t = useScopedI18n('hostPages.verifyPage');
  const [selectedCountries, setSelectedCountries] = React.useState<any[]>([]);
  const [error, setError] = React.useState('');
  const { accountData, savedAccountData, verifyAccountStatus, handleStep, setAccountData } = useVerifyState();

  const { data: countriesData } = useGetVerificationCountries();
  const countries = countriesData?.data?.data;
  const isVerified = verifyAccountStatus === 'success';
  const isStepDisabled = savedAccountData?.country?.length > 0 || isVerified;

  const handleCountries = (long_name: string) => {
    setError('');

    const fullCountry = countries?.find((country) => country.long_name === long_name);
    if (!fullCountry) return;

    if (!selectedCountries.some((country) => country.long_name === fullCountry.long_name)) {
      const updatedCountries = [...selectedCountries, fullCountry];
      setSelectedCountries(updatedCountries);
    }
  };

  const getCountriesByShortNames = (shortNames = ['KSA', 'PLE']) => {
    const hashArray = arrToHashTable(countries || [], 'short_name');
    return shortNames.map((shortName) => hashArray[shortName]).filter(Boolean);
  };

  const removeCountry = (short_name) => {
    const newSelectedCountries = selectedCountries.filter((country) => country.short_name !== short_name);
    setSelectedCountries(newSelectedCountries);
  };

  const onClickNext = () => {
    if (selectedCountries.length === 0) return setError(t('please_select_countries'));

    handleStep({ id: 'choose_country', active: 1 });

    if (!isStepDisabled) {
      setAccountData({ country_short_names: selectedCountries.map((country) => country.short_name) });
    }
  };

  useEffect(() => {
    if (accountData.country_short_names) {
      setSelectedCountries(getCountriesByShortNames(accountData.country_short_names));
    }
  }, [accountData.country_short_names, Boolean(countries?.length)]);

  return (
    <div className="flex flex-col gap-4">
      <div className="mobile:w-3/4 lg:w-1/2">
        <CusSelect
          label={t('please_select_countries')}
          data={getCountriesByShortNames()}
          keyField="id"
          valueField="long_name"
          labelField="long_name"
          value=""
          onChange={handleCountries}
          placeholder={t('choose_countries')}
          disabled={isStepDisabled}
        />
      </div>
      {selectedCountries.length > 0 && (
        <>
          <div className="text-secondary-300 text-md mt-4 font-bold">{t('selected_countries')}</div>
          <div className="flex gap-4">
            {selectedCountries.map((country) => (
              <div
                key={country.short_name}
                className="bg-white-full relative flex items-center gap-2 rounded-full px-3 py-2 shadow-md">
                {!isStepDisabled && (
                  <button
                    type="button"
                    title="remove"
                    onClick={() => removeCountry(country.short_name)}
                    className="bg-primary absolute -left-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full text-white hover:bg-red-200 focus:outline-none">
                    <svg
                      className="h-3 w-3"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
                <img src={country.flag} alt={country.long_name} className="h-4 w-6 rounded shadow-sm" />
                <span className="text-sm font-medium text-gray-800">{country.long_name}</span>
              </div>
            ))}
          </div>
        </>
      )}
      {error && <div className="text-primary-300 mt-1 px-3 text-sm">{error}</div>}

      <StepsBtn onClick={onClickNext} className="mobile:mt-4">
        {t('next')}
      </StepsBtn>
    </div>
  );
};

export default ChooseCountries;

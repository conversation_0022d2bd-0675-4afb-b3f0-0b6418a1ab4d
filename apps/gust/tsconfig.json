{
  "extends": "tsconfig/nextjs.json",
  "compilerOptions": {
    "typeRoots": ["./node_modules/@types", "./node_modules/@ably/chat/dist/react"],
    "moduleResolution": "bundler",
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"]
    },
    "jsx": "preserve",
    
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "components/icons/heart.tsx",
    "../../next-sitemap.config.ts",
    "components/pages/account/profile/edit-phone-number-modal"
  ],
  "exclude": ["node_modules"]
}
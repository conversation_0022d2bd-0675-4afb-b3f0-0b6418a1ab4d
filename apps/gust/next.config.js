/** @type {import('next').NextConfig} */
const nextConfig = {
  compiler: {
    removeConsole: {
      exclude: ['error', 'log', 'warning'],
    },
  },
  reactStrictMode: true,
  output: 'standalone',
  env: {
    NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV || 'development',
    NEXT_PUBLIC_BACKEND_URL: process.env.NEXT_PUBLIC_BACKEND_URL,
    NEXT_PUBLIC_GOOGLE_MAP_API_KEY: process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY,
    NEXT_PUBLIC_GEO_IP_ADDRESS: process.env.NEXT_PUBLIC_GEO_IP_ADDRESS || 'https://ipapi.co/json',
    NEXT_PUBLIC_PHONE_RETRY_TIME: process.env.NEXT_PUBLIC_PHONE_RETRY_TIME || 120,
    NEXT_PUBLIC_HOST_URL: process.env.NEXT_PUBLIC_HOST_URL,
    NEXT_PUBLIC_ABLY_API_KEY: process.env.NEXT_PUBLIC_ABLY_API_KEY,
    NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
    NEXT_PUBLIC_GOOGLE_MAP_ID: process.env.NEXT_PUBLIC_GOOGLE_MAP_ID,
  },
  images: {
    domains: ['axnqxdrfqrex.compat.objectstorage.me-jeddah-1.oraclecloud.com'],

    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'rentoor-bucket.s3.eu-central-1.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'stg-v3.rentoor.com',
      },
      {
        hostname: 'localhost',
      },
      {
        hostname: 'maps.googleapis.com',
      },
      {
        hostname: 'images.pexels.com',
      },
    ],
  },
  webpack(config) {
    if (process.env.NEXT_WEBPACK_USEPOLLING) {
      config.watchOptions = {
        ignored: '**/node_modules',
        poll: 6000,
        aggregateTimeout: 3000,
      };
    }
    return config;
  },
};

module.exports = nextConfig;

// Injected content via Sentry wizard below

const { withSentryConfig } = require("@sentry/nextjs");

module.exports = withSentryConfig(
  module.exports,
  {
    // For all available options, see:
    // https://www.npmjs.com/package/@sentry/webpack-plugin#options

    org: "rentoor",
    project: "frontend-nextjs",

    // Only print logs for uploading source maps in CI
    silent: !process.env.CI,

    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    tunnelRoute: "/monitoring",

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: true,
  }
);
